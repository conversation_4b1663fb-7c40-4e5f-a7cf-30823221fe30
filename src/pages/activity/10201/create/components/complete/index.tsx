import Container from '@/components/Container';
import { QRCodeSVG } from 'qrcode.react';
import styles from './index.module.scss';
import { Box, Button } from '@alifd/next';
import { history } from '@ice/runtime';
import { useActivity } from '../../reducer';
import constant from '@/utils/constant';

export default function Complete() {
  const { state } = useActivity();
  const { activityUrl } = state.extra;

  const appInfo = JSON.parse(localStorage.getItem(constant.LZ_CURRENT_SHOP) || '{}');

  return (
    <div className={styles.complete}>
      <Container>
        <div className={styles['qr-img-container']}>
          <QRCodeSVG
            value={activityUrl || ''}
            className="qr-img"
          />
          <div className={styles['qr-img-des']}>
            <p className={styles['qr-img-message']}>{'活动保存成功'}</p>
            <p className={[styles['qr-img-tips'], 'text-red'].join(' ')}>
              重要提示：您的活动若多期同时在线，小程序端将默认显示最近一期活动内容~
            </p>
            <p className={styles['qr-img-use-taobao']}>
              <img alt={''} src="https://img.alicdn.com/imgextra/i1/155168396/O1CN01qZhZQI2BtQ7srifM1_!!155168396.png" />
              <span>请使用抖音APP扫一扫预览</span>
            </p>
          </div>
        </div>
      </Container>

      <Box
        direction="row"
        justify="center"
        style={{
          position: 'fixed',
          bottom: 22,
          left: '50%',
          transform: 'translateX(calc(-50% + 110px))',
        }}
      >
        <Button
          type="primary"
          style={{ width: 150 }}
          onClick={() => {
          history?.push('/activity/list');
        }}
        >暂不投放</Button>
      </Box>

    </div>

  );
}
